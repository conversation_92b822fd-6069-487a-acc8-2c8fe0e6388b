﻿using System;
using System.Runtime.InteropServices;
using SmartConnector.Tools;

namespace SmartConnector.EBOOPCClient
{
	// Token: 0x0200000A RID: 10
	public static class UCMECheckLicense
	{
		// Token: 0x06000037 RID: 55 RVA: 0x000057CC File Offset: 0x000039CC
		public static int CheckLicense(out UCMECheckLicense.UCMELicInfo licInfo)
		{
			licInfo = new UCMECheckLicense.UCMELicInfo();
			try
			{
				int num = 0;
				int num2 = 0;
				int num3 = 0;
				int num4 = 0;
				if (UCMECheckLicense.CheckLicense(out num, out num2, out num3, out num4) == 0 && num4 >= Global.CURRENT_VERSION_NUMBER)
				{
					licInfo.m_nVer = num4;
					if (num == -1 && num2 == -1 && num3 == -1)
					{
						licInfo.m_ExpertionUnlimited = true;
						licInfo.m_bDemo = false;
					}
					if (num > 0 && num2 > 0 && num3 > 0)
					{
						if (num3 < 100)
						{
							num3 += 2000;
						}
						TimeSpan t = new TimeSpan(1, 0, 0, 0);
						licInfo.m_dtExpierd = new DateTime(num3, num2, num);
						licInfo.m_dtExpierd += t;
						licInfo.m_ExpertionUnlimited = false;
						if (DateTime.Now < licInfo.m_dtExpierd)
						{
							licInfo.m_bDemo = false;
						}
					}
					return 0;
				}
			}
			catch (Exception ex)
			{
				InitApp._ILog.Write(ILog.LogLevels.ERROR, "Check License fail, error '{0}'", new object[]
				{
					ex.Message
				});
				return -1;
			}
			return -1;
		}

		// Token: 0x06000038 RID: 56
		[DllImport("CheckLicense.dll", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
		public static extern int CheckLicense(out int dd, out int mm, out int yy, out int ver);

		// Token: 0x0200001F RID: 31
		public class UCMELicInfo
		{
			// Token: 0x040000A7 RID: 167
			public DateTime m_dtExpierd;

			// Token: 0x040000A8 RID: 168
			public bool m_ExpertionUnlimited;

			// Token: 0x040000A9 RID: 169
			public int m_nVer = -1;

			// Token: 0x040000AA RID: 170
			public bool m_bDemo = true;
		}
	}
}
