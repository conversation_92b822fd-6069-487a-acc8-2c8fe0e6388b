﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using Ews.Common;
using Microsoft.CSharp.RuntimeBinder;
using Mongoose.Common;
using Mongoose.Common.Attributes;
using Mongoose.Ews.Server.Data;
using Mongoose.Ews.Server.Data.Shared;
using Mongoose.Process;
using SmartConnector.Tools;

namespace SmartConnector.EBOOPCClient
{
	// Token: 0x0200000D RID: 13
	[ConfigurationDefaults("EBO-OPC Client Setup Processor", "EBO-OPC client EWS Server settings.")]
	public class SetupProcessor : EBOOpcClientProcessorBase, ILongRunningProcess
	{
		// Token: 0x1700000B RID: 11
		// (get) Token: 0x06000061 RID: 97 RVA: 0x000074E4 File Offset: 0x000056E4
		// (set) Token: 0x06000062 RID: 98 RVA: 0x000074EC File Offset: 0x000056EC
		[Required]
		[DefaultValue("http://localhost:5300/EcoStruxure/DataExchange")]
		[Tooltip("Default HTTP address to configure when bootstraping the EWS Server")]
		public string EwsAddress { get; set; }

		// Token: 0x1700000C RID: 12
		// (get) Token: 0x06000063 RID: 99 RVA: 0x000074F5 File Offset: 0x000056F5
		// (set) Token: 0x06000064 RID: 100 RVA: 0x000074FD File Offset: 0x000056FD
		[DefaultValue("CustomRealm")]
		[Tooltip("Realm value for HTTP Digest Authentication")]
		public string Realm { get; set; }

		// Token: 0x06000065 RID: 101 RVA: 0x00007508 File Offset: 0x00005708
		protected override IEnumerable<Prompt> Execute_Subclass()
		{
			if (!base.IsConnected)
			{
				return new List<Prompt>
				{
					base.CreateCannotConnectPrompt()
				};
			}
			if (!base.DataAdapter.Server.IsRunning)
			{
				base.DataAdapter.StartServer();
			}
			this.EnsureServerParameters();
			EBOOpcClientProcessorBase._IsInit = true;
			if ((EBOOpcClientProcessorBase._InitStatus = InitApp.InitApplication()) != 0)
			{
				return new List<Prompt>
				{
					base.InitFailPrompt()
				};
			}
			string text = Path.Combine(base.OPCClientFolder, "CreateEWSitems.dat");
			if (File.Exists(Path.Combine(new string[]
			{
				text
			})))
			{
				InitApp._ILog.Write(ILog.LogLevels.INFO, "CreateEWSitems.dat file has found");
				try
				{
					File.Delete(text);
					InitApp._ILog.Write(ILog.LogLevels.INFO, "CreateEWSitems.dat file has been deleted");
				}
				catch (Exception)
				{
					InitApp._ILog.Write(ILog.LogLevels.ERROR, "fail to delete CreateEWSitems.dat file!");
				}
				InitApp._ILog.Write(ILog.LogLevels.INFO, "Purge all Data");
				base.DataAdapter.PurgeData();
				InitApp._ILog.Write(ILog.LogLevels.INFO, "adding all EWS items...");
				for (int i = 0; i < ServerHelper._lstOpcClientManager.Count; i++)
				{
					OpcClientManager opcClientManager = ServerHelper._lstOpcClientManager[i];
					this.AddOPCServerItems(opcClientManager);
				}
				InitApp._ILog.Write(ILog.LogLevels.INFO, "adding all EWS items done");
			}
			else
			{
				InitApp._ILog.Write(ILog.LogLevels.INFO, "CreateEWSitems.dat file not found");
			}
			ServerHelper._SetupProcessorRunandEnded = true;
			if (EBOOpcClientProcessorBase._InitStatus != 0)
			{
				return new List<Prompt>
				{
					base.InitFailPrompt()
				};
			}
			return new List<Prompt>();
		}

		// Token: 0x06000066 RID: 102 RVA: 0x0000768C File Offset: 0x0000588C
		protected override EwsServerDataAdapter CreateEwsServer()
		{
			return EwsServerDataAdapter.ConnectNew(base.ServerName, this.EwsAddress, this.Realm, base.UserName, base.Password, true, true, "SmartConnector.EBOOPCClient.dll", "SmartConnector.EBOOPCClient.EwsServer.CustomEwsServiceHost");
		}

		// Token: 0x06000067 RID: 103 RVA: 0x000076C8 File Offset: 0x000058C8
		private void EnsureServerParameters()
		{
			base.CheckCancellationToken();
			base.DataAdapter.ModifyServerIsAutoStart(true);
			base.DataAdapter.ModifyServerAllowCookies(true);
			base.DataAdapter.ModifyServerPageSize(1000);
			base.DataAdapter.ModifyServerRootContainerItemAlternateId("RootContainer");
			base.DataAdapter.ModifyServerRootContainerItemDescription("All folders derive from here");
			this.EnsureSupportedMethods();
		}

		// Token: 0x06000068 RID: 104 RVA: 0x00007730 File Offset: 0x00005930
		private void EnsureSupportedMethods()
		{
			base.CheckCancellationToken();
			base.DataAdapter.ModifyServerSupportedMethods(new EwsServerMethods
			{
				AcknowledgeAlarmEvents = false,
				ForceValues = false,
				GetAlarmEventTypes = false,
				GetAlarmEvents = false,
				GetAlarmHistory = false,
				GetEnums = false,
				GetHierarchicalInformation = false,
				GetUpdatedAlarmEvents = false,
				UnforceValues = false,
				GetContainerItems = true,
				GetHistory = true,
				GetItems = true,
				GetNotification = true,
				GetValues = true,
				GetWebServiceInformation = true,
				Renew = true,
				SetValues = true,
				Subscribe = true,
				Unsubscribe = true
			});
		}

		// Token: 0x06000069 RID: 105 RVA: 0x000077DC File Offset: 0x000059DC
		private EwsContainerItem EnsureContainerItem(string altId, string name = null, string description = null, EwsContainerTypeEnum type = 0, EwsContainerItem parent = null)
		{
			base.CheckCancellationToken();
			EwsContainerItem ewsContainerItem = base.DataAdapter.ContainerItems.FirstOrDefault((EwsContainerItem x) => x.AlternateId == altId);
			if (ewsContainerItem == null)
			{
				return base.DataAdapter.AddContainerItem(altId, name ?? altId, description, type, parent);
			}
			ewsContainerItem = base.DataAdapter.ModifyContainerItemName(ewsContainerItem, altId);
			ewsContainerItem = base.DataAdapter.ModifyContainerItemDescription(ewsContainerItem, description);
			ewsContainerItem = base.DataAdapter.ModifyContainerItemType(ewsContainerItem, type);
			return base.DataAdapter.ModifyContainerItemParent(ewsContainerItem, parent);
		}

		// Token: 0x0600006A RID: 106 RVA: 0x000078D4 File Offset: 0x00005AD4
		private EwsValueItem EnsureValueItem(string altId, string name = null, string description = null, EwsValueTypeEnum type = 2, EwsContainerItem parent = null, string unit = null, EwsValueWriteableEnum writeable = 1, EwsValueForceableEnum forceable = 1, EwsValueStateEnum defaultState = 1)
		{
			base.CheckCancellationToken();
			EwsValueItem ewsValueItem = base.DataAdapter.ValueItems.FirstOrDefault((EwsValueItem x) => x.AlternateId == altId);
			if (ewsValueItem == null)
			{
				return base.DataAdapter.AddValueItem(altId, name ?? altId, description, type, writeable, forceable, defaultState, unit, parent, null);
			}
			ewsValueItem = base.DataAdapter.ModifyValueItemName(ewsValueItem, altId);
			ewsValueItem = base.DataAdapter.ModifyValueItemDescription(ewsValueItem, description);
			ewsValueItem = base.DataAdapter.ModifyValueItemType(ewsValueItem, type);
			ewsValueItem = base.DataAdapter.ModifyValueItemWriteable(ewsValueItem, writeable);
			ewsValueItem = base.DataAdapter.ModifyValueItemForceable(ewsValueItem, forceable);
			ewsValueItem = base.DataAdapter.ModifyValueItemUnit(ewsValueItem, unit);
			return base.DataAdapter.ModifyValueItemParent(ewsValueItem, parent);
		}

		// Token: 0x0600006B RID: 107 RVA: 0x00007A00 File Offset: 0x00005C00
		private EwsHistoryItem EnsureHistoryItem(string altId, string name, string description, EwsValueItem valueItem, EwsContainerItem parent = null)
		{
			base.CheckCancellationToken();
			EwsHistoryItem ewsHistoryItem = base.DataAdapter.HistoryItems.FirstOrDefault((EwsHistoryItem x) => x.AlternateId == altId);
			if (ewsHistoryItem == null)
			{
				return base.DataAdapter.AddHistoryItem(altId, name ?? altId, description, valueItem, true, parent);
			}
			ewsHistoryItem = base.DataAdapter.ModifyHistoryItemName(ewsHistoryItem, altId);
			ewsHistoryItem = base.DataAdapter.ModifyHistoryItemDescription(ewsHistoryItem, description);
			return base.DataAdapter.ModifyHistoryItemParent(ewsHistoryItem, parent);
		}

		// Token: 0x0600006C RID: 108 RVA: 0x00007AE8 File Offset: 0x00005CE8
		private void AddOPCServerItems(OpcClientManager opcClientManager)
		{
			EwsContainerItem parent = this.EnsureContainerItem(opcClientManager._OPCLogicalName, null, null, 0, null);
			this.EnsureValueItem(string.Format("[{0}].{1}", opcClientManager._OPCServerIndex, "OPC.Status"), "OPC.Status", "OPC.Status", 5, parent, null, 0, 0, 1);
			for (int i = 0; i < opcClientManager._lstOPCItems.Count; i++)
			{
				EBOOpcItem eboopcItem = opcClientManager._lstOPCItems[i];
				EwsContainerItem parent2 = this.EnsureContainerItem(string.Format("[{0}].{1}", opcClientManager._OPCServerIndex, eboopcItem._GroupName), null, null, 0, parent);
				EwsValueTypeEnum ewsValueTypeEnum = SetupProcessor.ConvertOPCDateTypeToEWSType(eboopcItem._DataTypeFromUCME);
				InitApp._ILog.Write(ILog.LogLevels.DEBUG, "add EWS item '{0}', type {1}, # of items {2} of {3}", new object[]
				{
					eboopcItem._EWSItemID,
					ewsValueTypeEnum,
					i,
					opcClientManager._lstOPCItems.Count
				});
				if (eboopcItem._NumOfItemsInArray <= 1)
				{
					this.EnsureValueItem(string.Format("[{0}].{1}", opcClientManager._OPCServerIndex, eboopcItem._EWSItemID), eboopcItem._EWSItemID, eboopcItem._AccessPath, ewsValueTypeEnum, parent2, null, 1, 1, 1);
				}
				else
				{
					InitApp._ILog.Write(ILog.LogLevels.DEBUG, "add EWS array items: '{0}', type {1}, # of array elements: {2} ", new object[]
					{
						eboopcItem._EWSItemID,
						ewsValueTypeEnum,
						eboopcItem._NumOfItemsInArray
					});
					for (int j = 0; j < eboopcItem._NumOfItemsInArray; j++)
					{
						this.EnsureValueItem(string.Format("[{0}].{1}#[{2}]", opcClientManager._OPCServerIndex, eboopcItem._EWSItemID, j + 1), string.Format("{0}#[{1}]", eboopcItem._EWSItemID, j + 1), string.Format("{0}#[{1}]", eboopcItem._AccessPath, j + 1), ewsValueTypeEnum, parent2, null, 1, 1, 1);
					}
				}
			}
		}

		// Token: 0x0600006D RID: 109 RVA: 0x00007CD0 File Offset: 0x00005ED0
		public static EwsValueTypeEnum ConvertOPCDateTypeToEWSType(string opcDataType)
		{
			uint num = <PrivateImplementationDetails>.ComputeStringHash(opcDataType);
			if (num > 1683620383U)
			{
				if (num <= 2797886853U)
				{
					if (num != 2579740217U)
					{
						if (num != 2699759368U)
						{
							if (num != 2797886853U)
							{
								return 2;
							}
							if (!(opcDataType == "float"))
							{
								return 2;
							}
						}
						else if (!(opcDataType == "double"))
						{
							return 2;
						}
						return 3;
					}
					if (!(opcDataType == "UI64"))
					{
						return 2;
					}
				}
				else if (num <= 2837873542U)
				{
					if (num != 2823553821U)
					{
						if (num != 2837873542U)
						{
							return 2;
						}
						if (!(opcDataType == "I64"))
						{
							return 2;
						}
					}
					else
					{
						if (!(opcDataType == "char"))
						{
							return 2;
						}
						return 5;
					}
				}
				else if (num != 3365180733U)
				{
					if (num != 3437915536U)
					{
						return 2;
					}
					if (!(opcDataType == "datetime"))
					{
						return 2;
					}
					return 0;
				}
				else
				{
					if (!(opcDataType == "bool"))
					{
						return 2;
					}
					return 1;
				}
				return 4;
			}
			if (num <= 633095128U)
			{
				if (num != 398550328U)
				{
					if (num != 565690462U)
					{
						if (num != 633095128U)
						{
							return 2;
						}
						if (!(opcDataType == "UI16"))
						{
							return 2;
						}
					}
					else if (!(opcDataType == "UI32"))
					{
						return 2;
					}
				}
				else
				{
					if (!(opcDataType == "string"))
					{
						return 2;
					}
					return 2;
				}
			}
			else if (num != 758287429U)
			{
				if (num != 959324667U)
				{
					if (num != 1683620383U)
					{
						return 2;
					}
					if (!(opcDataType == "byte"))
					{
						return 2;
					}
				}
				else if (!(opcDataType == "I16"))
				{
					return 2;
				}
			}
			else if (!(opcDataType == "I32"))
			{
				return 2;
			}
			return 5;
		}

		// Token: 0x0600006E RID: 110 RVA: 0x00007E94 File Offset: 0x00006094
		private void AddUserInputFields()
		{
			EwsValueItem ewsValueItem = this.EnsureValueItem("CityCode", "City Code", null, 4, null, null, 1, 0, 1);
			bool flag = string.IsNullOrEmpty(ewsValueItem.Value);
			if (!flag)
			{
				if (SetupProcessor.<>o__17.<>p__2 == null)
				{
					SetupProcessor.<>o__17.<>p__2 = CallSite<Func<CallSite, object, bool>>.Create(Binder.UnaryOperation(CSharpBinderFlags.None, ExpressionType.IsTrue, typeof(SetupProcessor), new CSharpArgumentInfo[]
					{
						CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, null)
					}));
				}
				Func<CallSite, object, bool> target = SetupProcessor.<>o__17.<>p__2.Target;
				CallSite <>p__ = SetupProcessor.<>o__17.<>p__2;
				if (SetupProcessor.<>o__17.<>p__1 == null)
				{
					SetupProcessor.<>o__17.<>p__1 = CallSite<Func<CallSite, bool, object, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.BinaryOperationLogical, ExpressionType.Or, typeof(SetupProcessor), new CSharpArgumentInfo[]
					{
						CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType, null),
						CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, null)
					}));
				}
				Func<CallSite, bool, object, object> target2 = SetupProcessor.<>o__17.<>p__1.Target;
				CallSite <>p__2 = SetupProcessor.<>o__17.<>p__1;
				bool arg = flag;
				if (SetupProcessor.<>o__17.<>p__0 == null)
				{
					SetupProcessor.<>o__17.<>p__0 = CallSite<Func<CallSite, object, int, object>>.Create(Binder.BinaryOperation(CSharpBinderFlags.None, ExpressionType.Equal, typeof(SetupProcessor), new CSharpArgumentInfo[]
					{
						CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.None, null),
						CSharpArgumentInfo.Create(CSharpArgumentInfoFlags.UseCompileTimeType | CSharpArgumentInfoFlags.Constant, null)
					}));
				}
				if (!target(<>p__, target2(<>p__2, arg, SetupProcessor.<>o__17.<>p__0.Target(SetupProcessor.<>o__17.<>p__0, ewsValueItem.GetValue(), 0))))
				{
					goto IL_13E;
				}
			}
			ewsValueItem = base.DataAdapter.ModifyValueItemValue(ewsValueItem, new long?(4929055L), null);
			IL_13E:
			EwsValueItem ewsValueItem2 = this.EnsureValueItem("CityName", "City Name", null, 2, null, null, 0, 0, 1);
			if (string.IsNullOrEmpty(ewsValueItem2.Value))
			{
				ewsValueItem2 = base.DataAdapter.ModifyValueItemValue(ewsValueItem2, string.Empty, new EwsValueStateEnum?(1));
			}
		}

		// Token: 0x0600006F RID: 111 RVA: 0x0000801C File Offset: 0x0000621C
		private void AddCurrentConditionPlaceHolders()
		{
			EwsContainerItem parent = this.EnsureContainerItem("CurrentConditions", null, null, 0, null);
			this.EnsureValueItem("CurrentConditions.LastUpdated", "Last Updated", null, 0, parent, null, 0, 0, 1);
			EwsValueItem valueItem = this.EnsureValueItem("CurrentConditions.Temperature", "Temperature", null, 3, parent, "°C", 0, 0, 1);
			this.EnsureHistoryItem("CurrentConditions.Temperature.History", null, null, valueItem, parent);
			EwsValueItem valueItem2 = this.EnsureValueItem("CurrentConditions.Pressure", "Pressure", null, 3, parent, "hPA", 0, 0, 1);
			this.EnsureHistoryItem("CurrentConditions.Pressure.History", null, null, valueItem2, parent);
			EwsValueItem valueItem3 = this.EnsureValueItem("CurrentConditions.RelativeHumidity", "Relative Humidity", null, 5, parent, "%", 0, 0, 1);
			this.EnsureHistoryItem("CurrentConditions.RelativeHumidity.History", null, null, valueItem3, parent);
		}
	}
}
