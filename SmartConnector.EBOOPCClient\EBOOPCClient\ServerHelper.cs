﻿using System;
using System.Collections.Generic;
using Ews.Common;
using Mongoose.Ews.Server.Data;
using SxL.Common;

namespace SmartConnector.EBOOPCClient
{
	// Token: 0x02000009 RID: 9
	public static class ServerHelper
	{
		// Token: 0x17000003 RID: 3
		// (get) Token: 0x06000034 RID: 52 RVA: 0x000055DC File Offset: 0x000037DC
		public static List<string> VolatileValueItemIds
		{
			get
			{
				if (ServerHelper._volatileValueItemIds != null)
				{
					return ServerHelper._volatileValueItemIds;
				}
				ServerHelper._volatileValueItemIds = new List<string>
				{
					"CurrentConditions.LastUpdated",
					"CurrentConditions.Temperature",
					"CurrentConditions.Pressure",
					"CurrentConditions.RelativeHumidity",
					"CurrentConditions.Temperature.History",
					"CurrentConditions.Pressure.History"
				};
				for (int i = 1; i <= 3; i++)
				{
					ServerHelper._volatileValueItemIds.Add(string.Format("Forecast.Day{0}", i));
					ServerHelper._volatileValueItemIds.Add(string.Format("Forecast.Day{0}.Date", i));
					ServerHelper._volatileValueItemIds.Add(string.Format("Forecast.Day{0}.HighTemp", i));
					ServerHelper._volatileValueItemIds.Add(string.Format("Forecast.Day{0}.LowTemp", i));
					ServerHelper._volatileValueItemIds.Add(string.Format("Forecast.Day{0}.Pressure", i));
					ServerHelper._volatileValueItemIds.Add(string.Format("Forecast.Day{0}.RelativeHumidity", i));
					ServerHelper._volatileValueItemIds.Add(string.Format("Forecast.Day{0}.Description", i));
				}
				return ServerHelper._volatileValueItemIds;
			}
		}

		// Token: 0x06000035 RID: 53 RVA: 0x00005710 File Offset: 0x00003910
		public static void MakeDataServedUncertain(EwsServerDataAdapter adapter, string cityName, string logCategory)
		{
			adapter.ModifyValueItemValue("CityName", cityName, new EwsValueStateEnum?(1));
			foreach (string text in ServerHelper.VolatileValueItemIds)
			{
				try
				{
					adapter.ModifyValueItemState(text, 1);
				}
				catch (Exception ex)
				{
					Logger.LogError(logCategory, ex, Array.Empty<object>());
				}
			}
		}

		// Token: 0x04000041 RID: 65
		public static string _OPCClientFolder = "C:\\Program Files (x86)\\Control-See\\EBO-OPC client";

		// Token: 0x04000042 RID: 66
		public static string _OPCClientLogFolder = "C:\\Program Files (x86)\\Control-See\\EBO-OPC client\\Logs";

		// Token: 0x04000043 RID: 67
		public static List<OpcClientManager> _lstOpcClientManager = new List<OpcClientManager>();

		// Token: 0x04000044 RID: 68
		public static bool _UpdateProcessorStop = false;

		// Token: 0x04000045 RID: 69
		public static bool _SetupProcessorRunandEnded = false;

		// Token: 0x04000046 RID: 70
		public static UpdateProcessor _UpdateProcessor = null;

		// Token: 0x04000047 RID: 71
		public const string CityId = "CityCode";

		// Token: 0x04000048 RID: 72
		public const string CityNameId = "CityName";

		// Token: 0x04000049 RID: 73
		public const string CurrentLastUpdateId = "CurrentConditions.LastUpdated";

		// Token: 0x0400004A RID: 74
		public const string CurrentTemperatureId = "CurrentConditions.Temperature";

		// Token: 0x0400004B RID: 75
		public const string CurrentPressureId = "CurrentConditions.Pressure";

		// Token: 0x0400004C RID: 76
		public const string CurrentRelativeHumidityId = "CurrentConditions.RelativeHumidity";

		// Token: 0x0400004D RID: 77
		public const string CurrentTemperatureHistoryId = "CurrentConditions.Temperature.History";

		// Token: 0x0400004E RID: 78
		public const string CurrentPressureHistoryId = "CurrentConditions.Pressure.History";

		// Token: 0x0400004F RID: 79
		public const string CurrentRelativeHumidityHistoryId = "CurrentConditions.RelativeHumidity.History";

		// Token: 0x04000050 RID: 80
		public const int ForecastNumberOfDays = 3;

		// Token: 0x04000051 RID: 81
		public const string ForecastDayContainerId = "Forecast.Day{0}";

		// Token: 0x04000052 RID: 82
		public const string ForecastDayDateId = "Forecast.Day{0}.Date";

		// Token: 0x04000053 RID: 83
		public const string ForecastDayHighTempId = "Forecast.Day{0}.HighTemp";

		// Token: 0x04000054 RID: 84
		public const string ForecastDayLowTempId = "Forecast.Day{0}.LowTemp";

		// Token: 0x04000055 RID: 85
		public const string ForecastDayPressureId = "Forecast.Day{0}.Pressure";

		// Token: 0x04000056 RID: 86
		public const string ForecastDayRelativeHumidityId = "Forecast.Day{0}.RelativeHumidity";

		// Token: 0x04000057 RID: 87
		public const string ForecastDayDescriptionId = "Forecast.Day{0}.Description";

		// Token: 0x04000058 RID: 88
		private static List<string> _volatileValueItemIds;
	}
}
