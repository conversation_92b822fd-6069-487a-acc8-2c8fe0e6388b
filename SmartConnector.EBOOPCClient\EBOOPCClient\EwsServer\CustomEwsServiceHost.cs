﻿using System;
using System.Collections.Generic;
using Ews.Server.Contract;
using Mongoose.Common;
using Mongoose.Common.Data.Licensing;
using Mongoose.Ews.Server.Data;

namespace SmartConnector.EBOOPCClient.EwsServer
{
	// Token: 0x0200001A RID: 26
	public class CustomEwsServiceHost : EwsServiceHost
	{
		// Token: 0x060000E0 RID: 224 RVA: 0x00008435 File Offset: 0x00006635
		public CustomEwsServiceHost(EwsServer serverConfiguration) : base(typeof(CustomDataExchange), serverConfiguration)
		{
		}

		// Token: 0x1700003E RID: 62
		// (get) Token: 0x060000E1 RID: 225 RVA: 0x00008448 File Offset: 0x00006648
		public override bool IsLicensed
		{
			get
			{
				return false;
			}
		}

		// Token: 0x060000E2 RID: 226 RVA: 0x0000844B File Offset: 0x0000664B
		public override IEnumerable<Prompt> ValidateCustomLicenseFeatures(ExtensionLicense license)
		{
			throw new NotImplementedException("Implementing class does not support licensing");
		}

		// Token: 0x060000E3 RID: 227 RVA: 0x00008457 File Offset: 0x00006657
		protected override void ProvisionEndpoint()
		{
			base.AddServiceEndpoint(typeof(IDataExchange), EwsServiceHost.CreateBinding(base.IsHttps), base.ServerAddress);
		}
	}
}
